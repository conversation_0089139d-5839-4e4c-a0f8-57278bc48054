import { DataTypes as SequelizeDataTypes, DataType as SequelizeDataTypeInterface } from "sequelize";
import { IAttibutes, IDbConfig, IModelOptions } from "../types";
export declare class SequelizerAdaptor {
    private sequelize;
    constructor(dbConfig: IDbConfig);
    define(modelName: string, attributes: IAttibutes, options?: IModelOptions): void;
    private formatAttributes;
}
export { SequelizeDataTypes, SequelizeDataTypeInterface };
