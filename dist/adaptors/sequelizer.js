"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SequelizeDataTypes = exports.SequelizerAdaptor = void 0;
const sequelize_1 = require("sequelize");
Object.defineProperty(exports, "SequelizeDataTypes", { enumerable: true, get: function () { return sequelize_1.DataTypes; } });
class SequelizerAdaptor {
    constructor(dbConfig) {
        this.sequelize = new sequelize_1.Sequelize({
            database: dbConfig.database,
            username: dbConfig.username,
            password: dbConfig.password,
            host: dbConfig.host,
            dialect: dbConfig.dialect,
            port: dbConfig.port,
            pool: dbConfig.pool,
            schema: dbConfig.schema,
        });
    }
    define(modelName, attributes, options) {
        const formattedAttributes = this.formatAttributes(attributes);
        this.sequelize.define(modelName, formattedAttributes, options);
    }
    formatAttributes(attributes) {
        const formattedAttributes = {};
        for (const [attributeName, column] of Object.entries(attributes)) {
            formattedAttributes[attributeName] = {
                type: column.dataType,
                field: column.columnName || attributeName,
                primaryKey: column.isPrimaryKey || false,
                allowNull: column.isNullable !== false,
                unique: column.isUnique || false,
                autoIncrement: column.isAutoIncrement || false,
                defaultValue: column.defaultValue,
                onDelete: column.onDelete,
                onUpdate: column.onUpdate,
            };
        }
        return formattedAttributes;
    }
}
exports.SequelizerAdaptor = SequelizerAdaptor;
