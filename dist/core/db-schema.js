"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DbSchema = void 0;
const model_1 = require("./model");
const adaptors_1 = require("../adaptors");
class DbSchema {
    constructor(dbConfig) {
        this.entities = [];
        this.dbConfig = {};
        this.dbConfig = dbConfig;
        this.sequelizerAdaptor = new adaptors_1.SequelizerAdaptor(dbConfig);
    }
    setEntity(model) {
        this.entities.push(model);
        this.sequelizerAdaptor.define(model.getEntityName(), model.getAttributes(), model.getOptions());
        return model;
    }
    defineEntity(entityName, attributes, options) {
        const model = new model_1.Model(entityName, attributes, options);
        this.entities.push(model);
        this.sequelizerAdaptor.define(entityName, attributes, options);
        return model;
    }
    getDbConfig() {
        return this.dbConfig;
    }
}
exports.DbSchema = DbSchema;
