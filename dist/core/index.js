"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Model = exports.DbSchema = void 0;
const db_schema_1 = require("./db-schema");
Object.defineProperty(exports, "DbSchema", { enumerable: true, get: function () { return db_schema_1.DbSchema; } });
const model_1 = require("./model");
Object.defineProperty(exports, "Model", { enumerable: true, get: function () { return model_1.Model; } });
