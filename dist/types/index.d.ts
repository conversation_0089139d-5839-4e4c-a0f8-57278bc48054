import { DataTypes as SequelizeDataTypes, DataType as SequelizeDataTypeInterface } from "sequelize";
declare const DataTypes: typeof SequelizeDataTypes;
type IDataType = SequelizeDataTypeInterface;
interface IColumn {
    dataType: IDataType;
    columnName?: string;
    isPrimaryKey?: boolean;
    isNullable?: boolean;
    isUnique?: boolean;
    isAutoIncrement?: boolean;
    onDelete?: "CASCADE";
    onUpdate?: "CASCADE";
    defaultValue?: string | number | Date | boolean;
}
type IAttibutes = Record<string, IColumn>;
interface IModelOptions {
}
type Dialect = "mysql" | "postgres" | "sqlite" | "mariadb" | "mssql" | "db2" | "snowflake" | "oracle";
interface PoolOptions {
    max?: number;
    min?: number;
    idle?: number;
    acquire?: number;
    evict?: number;
    maxUses?: number;
}
interface IDbConfig {
    database: string;
    username: string;
    password: string;
    host: string;
    dialect: Dialect;
    port: number;
    pool: PoolOptions;
    schema: string;
}
export { IColumn, IAttibutes, DataTypes, IDataType, IModelOptions, IDbConfig, Dialect, PoolOptions, };
