import { DbSchema, ODataControler } from "../../src";

const createSchema = () => {
  const dbSchema = new DbSchema({
    dialect: "postgres",
    database: "twostapp",
    username: "sysadmin",
    password: "gFp0sd=wjJbB2y=D=Xq0FkvSSvgSOS",
    host: "twostapp-prod.chk68km08hmu.ca-central-1.rds.amazonaws.com",
    port: 5432,
    pool: {},
    schema: "public",
    ssl: true,
  });
  const user = dbSchema.defineEntity("user", {
    id: {
      dataType: "int",
      isPrimaryKey: true,
      isNullable: false,
    },
    user_key: {
      dataType: "uuid",
      isNullable: false,
    },
    email: {
      dataType: "email",
      isNullable: false,
      isUnique: true,
    },
    first_name: {
      dataType: "string",
      isNullable: false,
    },
    last_name: {
      dataType: "string",
      isNullable: false,
    },
    gender: {
      dataType: "string",
      isNullable: true,
    },
    created_at: {
      dataType: "date",
      isNullable: false,
    },
    updated_at: {
      dataType: "date",
      isNullable: false,
    },
  });

  // ❌ This will show TypeScript error and runtime error
  // user.attributes.ids; // Property 'ids' does not exist

  // ✅ This will work and show autocomplete
  console.log("------------->", user);
  class UserController extends ODataControler {
    constructor() {
      const config = {
        endpoint: "/users",
        allowedMethod: ["GET"],
      };
      super(user, config);
    }
    get() {
      return user;
    }
  }

  new UserController().get();

  return { dbSchema, user };
};

export { createSchema };
