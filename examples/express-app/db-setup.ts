import { DbSchema } from "../../src";

const createSchema = () => {
  const dbSchema = new DbSchema({
    dialect: "postgres",
    database: "twostapp",
    username: "sysadmin",
    password: "gFp0sd=wjJbB2y=D=Xq0FkvSSvgSOS",
    host: "twostapp-prod.chk68km08hmu.ca-central-1.rds.amazonaws.com",
    port: 5432,
    pool: {},
    schema: "public",
    ssl: true,
  });
  const User = dbSchema.defineEntity("user", {
    id: {
      dataType: "int",
      isPrimaryKey: true,
      isNullable: false,
    },
    user_key: {
      dataType: "uuid",
      isNullable: false,
    },
    email: {
      dataType: "email",
      isNullable: false,
      isUnique: true,
    },
    first_name: {
      dataType: "string",
      isNullable: false,
    },
    last_name: {
      dataType: "string",
      isNullable: false,
    },
    gender: {
      dataType: "string",
      isNullable: true,
    },
    created_at: {
      dataType: "date",
      isNullable: false,
    },
    updated_at: {
      dataType: "date",
      isNullable: false,
    },
  });
};
