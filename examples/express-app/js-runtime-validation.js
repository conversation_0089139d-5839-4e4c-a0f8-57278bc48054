// JavaScript example showing runtime validation
// This works in Node.js 16+ without TypeScript

const { DbSchema } = require("../../dist"); // Assuming compiled version

function createSchemaJS() {
  const dbSchema = new DbSchema({
    dialect: "postgres",
    database: "testdb",
    username: "user",
    password: "pass",
    host: "localhost",
    port: 5432,
    pool: {},
    schema: "public",
    ssl: false,
  });

  const user = dbSchema.defineEntity("user", {
    id: {
      dataType: "int",
      isPrimaryKey: true,
      isNullable: false,
    },
    email: {
      dataType: "email",
      isNullable: false,
      isUnique: true,
    },
    first_name: {
      dataType: "string",
      isNullable: false,
    },
    last_name: {
      dataType: "string",
      isNullable: false,
    },
  });

  user.attributes.console.log("=== JavaScript Runtime Validation Demo ===\n");

  // ✅ Valid field access
  console.log("✅ Valid field access:");
  console.log("ID field:", user.attributes.id);
  console.log("Email field:", user.attributes.email);

  // ✅ Helper methods
  console.log("\n✅ Helper methods:");
  console.log("Field names:", user.getFieldNames());
  console.log("Has 'email' field:", user.hasField("email"));
  console.log("Has 'invalid' field:", user.hasField("invalid"));

  // ❌ Invalid field access - will throw error
  console.log("\n❌ Invalid field access:");
  try {
    console.log("Trying to access user.attributes.invalid_field...");
    user.attributes.invalid_field; // This will throw an error
  } catch (error) {
    console.error("Runtime Error:", error.message);
  }

  // ❌ Invalid field via getField method
  console.log("\n❌ Invalid field via getField method:");
  try {
    user.getField("nonexistent");
  } catch (error) {
    console.error("Runtime Error:", error.message);
  }

  // ✅ Safe field access
  console.log("\n✅ Safe field access:");
  try {
    console.log("Email field metadata:", user.getField("email"));
  } catch (error) {
    console.error("Error:", error.message);
  }

  return { dbSchema, user };
}

// Run the demo
if (require.main === module) {
  try {
    createSchemaJS();
  } catch (error) {
    console.error("Setup error:", error.message);
  }
}

class UserController extends ODataControler {
  constructor() {
    super();
  }
  get(query) {
    return {};
  }
}

module.exports = { createSchemaJS };
