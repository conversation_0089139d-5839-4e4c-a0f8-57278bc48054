import {
  Sequelize,
  ModelAttributes,
  Model,
  DataTypes as SequelizeDataTypes,
  DataType as SequelizeDataTypeInterface,
  ModelCtor,
} from "sequelize";
import { IAttibutes, IDbConfig, IModelOptions } from "../types";

type SequelizeModelController = ModelCtor<Model<any, any>>;

export class SequelizerAdaptor {
  private sequelize: Sequelize;

  constructor(dbConfig: IDbConfig) {
    this.sequelize = new Sequelize({
      database: dbConfig.database,
      username: dbConfig.username,
      password: dbConfig.password,
      host: dbConfig.host,
      dialect: dbConfig.dialect,
      port: dbConfig.port,
      pool: dbConfig.pool,
      schema: dbConfig.schema,
      ssl: dbConfig.ssl,
    });
    const User = this.sequelize.define("test", {
      id: {
        type: SequelizeDataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
    });

    new User().validate({ fields: ["ids"] });
  }

  public define(
    modelName: string,
    attributes: IAttibutes,
    options?: IModelOptions
  ) {
    const formattedAttributes = this.formatAttributes(attributes);
    const model: SequelizeModelController = this.sequelize.define(
      modelName,
      formattedAttributes,
      options
    );
    return model;
  }

  private formatAttributes(
    attributes: IAttibutes
  ): ModelAttributes<Model<any, any>> {
    const formattedAttributes: ModelAttributes<Model<any, any>> = {};

    for (const [attributeName, column] of Object.entries(attributes)) {
      formattedAttributes[attributeName] = {
        type: column.dataType,
        field: column.columnName || attributeName,
        primaryKey: column.isPrimaryKey || false,
        allowNull: column.isNullable !== false, // Default to true if not explicitly set to false
        unique: column.isUnique || false,
        autoIncrement: column.isAutoIncrement || false,
        defaultValue: column.defaultValue,
        onDelete: column.onDelete,
        onUpdate: column.onUpdate,
      };
    }

    return formattedAttributes;
  }
}

export {
  SequelizeDataTypes,
  SequelizeDataTypeInterface,
  SequelizeModelController,
};
