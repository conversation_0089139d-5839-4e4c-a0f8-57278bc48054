import { IAttibutes, IDbConfig, IModelOptions } from "../types";
import { Model } from "./model";
import { SequelizerAdaptor } from "../adaptors";

// Type helper to create typed attributes from field definitions
type TypedAttributes<T extends IAttibutes> = {
  readonly [K in keyof T]: T[K];
};

// Enhanced Model type with typed attributes
export interface TypedModel<T extends IAttibutes> extends Model {
  attributes: TypedAttributes<T>;
}

export class DbSchema {
  private entities: Model[] = [];
  private dbConfig = {};
  private sequelizerAdaptor: SequelizerAdaptor;

  constructor(dbConfig: IDbConfig) {
    this.dbConfig = dbConfig;
    this.sequelizerAdaptor = new SequelizerAdaptor(dbConfig);
  }

  public setEntity(model: Model): Model {
    this.entities.push(model);
    this.sequelizerAdaptor.define(
      model.getEntityName(),
      model.getAttributes(),
      model.getOptions()
    );
    return model;
  }

  public defineEntity<T extends IAttibutes>(
    entityName: string,
    attributes: T,
    options?: IModelOptions
  ): TypedModel<T> {
    this.sequelizerAdaptor.define(entityName, attributes, options);
    const model = new Model(entityName, attributes, options);
    this.entities.push(model);
    return model as TypedModel<T>;
  }

  public getDbConfig() {
    return this.dbConfig;
  }
}
