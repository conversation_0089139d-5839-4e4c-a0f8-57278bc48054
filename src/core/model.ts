import { SequelizeModelController } from "../adaptors/sequelizer";
import { IModelOptions, IAttibutes } from "../types";

export class Model {
  public readonly entityName: string;
  public readonly attributes: IAttibutes;
  private readonly options: IModelOptions;
  private sequelizerModel?: SequelizeModelController;

  constructor(
    entityName: string,
    attributes: IAttibutes,
    options?: IModelOptions,
    sequelizerModel?: SequelizeModelController
  ) {
    this.entityName = entityName;
    this.attributes = attributes;
    this.options = options || {};
    this.sequelizerModel = sequelizerModel;
  }

  public getEntityName() {
    return this.entityName;
  }

  public getAttributes() {
    return this.attributes;
  }

  public getOptions() {
    return this.options;
  }

  public setSequelizerModel(model: SequelizeModelController) {
    return (this.sequelizerModel = model);
  }

  public getSequelizerModel() {
    return this.sequelizerModel;
  }

  /**
   * Get list of available field names
   */
  public getFieldNames(): string[] {
    return Object.keys(this.attributes);
  }

  /**
   * Check if a field exists
   */
  public hasField(fieldName: string): boolean {
    return fieldName in this.attributes;
  }

  /**
   * Get field metadata safely
   */
  public getField(fieldName: string) {
    if (!this.hasField(fieldName)) {
      const availableFields = this.getFieldNames().join(", ");
      throw new Error(
        `Field '${fieldName}' does not exist. Available fields: ${availableFields}`
      );
    }
    return this.attributes[fieldName];
  }
}
